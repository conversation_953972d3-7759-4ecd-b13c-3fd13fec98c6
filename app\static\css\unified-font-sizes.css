/* 统一字体大小设置 - 参考左侧导航栏 */
/* 
 * 字体大小标准：
 * - 大字体（一级菜单级别）：14px
 * - 小字体（二级菜单级别）：13px
 */

:root {
    /* 统一字体大小变量 */
    --font-size-large: 14px;  /* 一级菜单级别 - 主要内容 */
    --font-size-small: 13px;  /* 二级菜单级别 - 次要内容 */
}

/* === 主要内容区域 - 使用大字体 (14px) === */

/* 卡片内容 */
.card-body,
.card-text,
.card p,
.card div {
    font-size: var(--font-size-large) !important;
}

/* 表格内容 */
.table tbody td,
.table tbody th {
    font-size: var(--font-size-large) !important;
}

/* 表格表头 */
.table thead th {
    font-size: var(--font-size-large) !important;
}

/* 表单控件 */
.form-control,
.form-select,
.form-check-label,
input,
select,
textarea {
    font-size: var(--font-size-large) !important;
}

/* 表单标签 */
.form-label,
label {
    font-size: var(--font-size-large) !important;
}

/* 按钮文字 */
.btn {
    font-size: var(--font-size-large) !important;
}

/* 模态框内容 */
.modal-body,
.modal-body p,
.modal-body div {
    font-size: var(--font-size-large) !important;
}

/* 列表组 */
.list-group-item {
    font-size: var(--font-size-large) !important;
}

/* 警告框 */
.alert,
.alert p,
.alert div {
    font-size: var(--font-size-large) !important;
}

/* 面包屑导航 */
.breadcrumb,
.breadcrumb-item {
    font-size: var(--font-size-large) !important;
}

/* 主要文本内容 */
p, div, span, li {
    font-size: var(--font-size-large) !important;
}

/* === 次要内容区域 - 使用小字体 (13px) === */

/* 徽章 */
.badge {
    font-size: var(--font-size-small) !important;
}

/* 小按钮 */
.btn-sm {
    font-size: var(--font-size-small) !important;
}

/* 帮助文本 */
.form-text,
.text-muted,
.small,
small {
    font-size: var(--font-size-small) !important;
}

/* 分页 */
.pagination .page-link {
    font-size: var(--font-size-small) !important;
}

/* 工具提示 */
.tooltip,
.popover {
    font-size: var(--font-size-small) !important;
}

/* 下拉菜单项 */
.dropdown-item {
    font-size: var(--font-size-small) !important;
}

/* 表格内的小元素 */
.table .btn-sm,
.table .badge,
.table small {
    font-size: var(--font-size-small) !important;
}

/* 卡片标题保持原有大小 */
.card-header,
.card-title,
.card-header h1,
.card-header h2,
.card-header h3,
.card-header h4,
.card-header h5,
.card-header h6 {
    /* 保持原有字体大小，不强制覆盖 */
}

/* 主标题保持原有大小 */
h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
    /* 保持原有字体大小，不强制覆盖 */
}

/* === 特殊情况处理 === */

/* 确保导航栏不受影响 */
.navbar,
.navbar-nav,
.nav-link,
.sidebar,
.sidebar-nav-link,
.sidebar-dropdown-item {
    /* 保持原有字体大小，不强制覆盖 */
}

/* 确保顶部工具栏不受影响 */
.top-toolbar,
.top-toolbar .btn {
    /* 保持原有字体大小，不强制覆盖 */
}

/* === 移动端适配 === */
@media (max-width: 768px) {
    :root {
        /* 移动端稍微调整字体大小 */
        --font-size-large: 15px;  /* 移动端增大到15px，便于阅读 */
        --font-size-small: 14px;  /* 移动端增大到14px */
    }
}

@media (max-width: 480px) {
    :root {
        /* 小屏幕进一步调整 */
        --font-size-large: 16px;  /* 小屏幕增大到16px */
        --font-size-small: 15px;  /* 小屏幕增大到15px */
    }
}

/* === 打印样式 === */
@media print {
    :root {
        /* 打印时使用较小字体节省空间 */
        --font-size-large: 12px;
        --font-size-small: 11px;
    }
}

/* === 高对比度模式 === */
@media (prefers-contrast: high) {
    :root {
        /* 高对比度模式下稍微增大字体 */
        --font-size-large: 15px;
        --font-size-small: 14px;
    }
}

/* === 用户偏好设置 === */
/* 支持用户自定义字体大小 */
.font-size-override-large {
    --font-size-large: 16px !important;
    --font-size-small: 15px !important;
}

.font-size-override-small {
    --font-size-large: 13px !important;
    --font-size-small: 12px !important;
}
