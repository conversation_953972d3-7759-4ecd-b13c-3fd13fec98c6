/**
 * 新版菜谱选择器 - 重写版本
 * 学习财务凭证页面的简洁设计理念
 * 使用原生JavaScript，Bootstrap 5.3.6兼容
 */

class RecipeSelectorV3 {
    constructor() {
        this.state = {
            currentDate: null,
            currentMeal: null,
            isLoading: false
        };
        
        this.selectedRecipes = new Map();
        this.availableRecipes = [];
        this.modal = null;
        
        this.init();
    }

    init() {
        console.log('初始化新版菜谱选择器 V3');
        this.initModal();
        this.bindEvents();
        this.loadRecipes();
    }

    // 初始化模态框
    initModal() {
        const modalElement = document.getElementById('menuModal');
        if (modalElement && typeof bootstrap !== 'undefined') {
            this.modal = new bootstrap.Modal(modalElement, {
                backdrop: 'static',
                keyboard: true
            });
        }
    }

    // 绑定事件
    bindEvents() {
        // 搜索功能 - 简单直接
        const searchInput = document.getElementById('recipeSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filterRecipes(e.target.value.trim());
            });
        }

        // 自定义菜品添加
        const addBtn = document.getElementById('addCustomDishBtn');
        const customInput = document.getElementById('customDishInput');
        
        if (addBtn) {
            addBtn.addEventListener('click', () => this.addCustomRecipe());
        }
        
        if (customInput) {
            customInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.addCustomRecipe();
                }
            });
        }

        // 保存选择
        const saveBtn = document.getElementById('saveSelectionBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveSelection());
        }

        // 来源筛选按钮
        document.addEventListener('click', (e) => {
            if (e.target.matches('#schoolRecipesBtn, #systemRecipesBtn')) {
                this.handleSourceFilter(e.target);
            }
        });

        // 菜品卡片点击 - 使用事件委托
        document.addEventListener('click', (e) => {
            const recipeCard = e.target.closest('.recipe-card');
            if (recipeCard) {
                this.selectRecipe(recipeCard);
            }
        });

        // 移除已选菜品
        document.addEventListener('click', (e) => {
            if (e.target.matches('.remove-btn')) {
                const tag = e.target.closest('.selected-recipe-tag');
                if (tag) {
                    this.removeRecipe(tag.dataset.id);
                }
            }
        });
    }

    // 显示模态框
    showModal(date, meal) {
        console.log(`显示菜谱选择器: ${date} ${meal}`);
        
        this.state.currentDate = date;
        this.state.currentMeal = meal;
        
        // 清空状态
        this.selectedRecipes.clear();
        this.clearSelectedDisplay();
        
        // 加载当前已选菜品
        this.loadCurrentRecipes(date, meal);
        
        // 更新标题
        const titleElement = document.getElementById('modalTitle');
        if (titleElement) {
            titleElement.textContent = `${date} ${meal} 菜品选择`;
        }
        
        // 显示模态框
        if (this.modal) {
            this.modal.show();
        }
        
        // 聚焦搜索框 - 学习财务凭证页面
        setTimeout(() => {
            const searchInput = document.getElementById('recipeSearch');
            if (searchInput) {
                searchInput.focus();
                searchInput.value = '';
            }
        }, 100);
    }

    // 加载菜谱数据
    async loadRecipes() {
        if (this.state.isLoading) return;
        
        this.state.isLoading = true;
        
        try {
            // 默认加载学校食谱
            await this.loadRecipesBySource('school');
        } catch (error) {
            console.error('加载菜谱失败:', error);
        } finally {
            this.state.isLoading = false;
        }
    }

    // 按来源加载菜谱
    async loadRecipesBySource(source) {
        try {
            const response = await fetch(`/api/recipes/filter?source=${source}`);
            const data = await response.json();
            
            if (data.success) {
                this.availableRecipes = this.flattenRecipes(data.data);
                this.renderRecipes(this.availableRecipes);
                this.updateCategoryTabs(data.data);
            }
        } catch (error) {
            console.error('加载菜谱数据失败:', error);
        }
    }

    // 扁平化菜谱数据
    flattenRecipes(recipesByCategory) {
        const recipes = [];
        Object.values(recipesByCategory).forEach(categoryRecipes => {
            if (Array.isArray(categoryRecipes)) {
                recipes.push(...categoryRecipes);
            }
        });
        return recipes;
    }

    // 渲染菜谱卡片
    renderRecipes(recipes) {
        const container = document.querySelector('.tab-content .row');
        if (!container) return;
        
        container.innerHTML = '';
        
        recipes.forEach(recipe => {
            const cardElement = this.createRecipeCard(recipe);
            container.appendChild(cardElement);
        });
    }

    // 创建菜谱卡片
    createRecipeCard(recipe) {
        const cardDiv = document.createElement('div');
        cardDiv.className = 'recipe-card-item';
        cardDiv.style.cssText = 'display: inline-block; width: 200px; margin: 10px; vertical-align: top;';
        
        cardDiv.innerHTML = `
            <div class="card recipe-card" data-id="${recipe.id}" data-name="${recipe.display_name}" data-category="${recipe.category}">
                <div class="card-body">
                    <h6 class="card-title">${recipe.display_name}</h6>
                </div>
            </div>
        `;
        
        return cardDiv;
    }

    // 更新分类标签
    updateCategoryTabs(recipesByCategory) {
        const categoriesContainer = document.getElementById('recipeCategories');
        if (!categoriesContainer) return;
        
        categoriesContainer.innerHTML = '<li class="nav-item"><a class="nav-link active" data-category="all" href="#">全部</a></li>';
        
        Object.keys(recipesByCategory).forEach(category => {
            const li = document.createElement('li');
            li.className = 'nav-item';
            li.innerHTML = `<a class="nav-link" data-category="${category}" href="#">${category}</a>`;
            categoriesContainer.appendChild(li);
        });
    }

    // 处理来源筛选
    handleSourceFilter(button) {
        // 更新按钮状态
        document.querySelectorAll('#schoolRecipesBtn, #systemRecipesBtn').forEach(btn => {
            btn.classList.remove('active');
        });
        button.classList.add('active');
        
        // 加载对应来源的菜谱
        const source = button.id === 'schoolRecipesBtn' ? 'school' : 'system';
        this.loadRecipesBySource(source);
    }

    // 筛选菜谱
    filterRecipes(keyword) {
        const cards = document.querySelectorAll('.recipe-card-item');
        
        cards.forEach(card => {
            const title = card.querySelector('.card-title');
            if (title) {
                const text = title.textContent.toLowerCase();
                const matches = !keyword || text.includes(keyword.toLowerCase());
                card.style.display = matches ? 'inline-block' : 'none';
            }
        });
    }

    // 选择菜谱
    selectRecipe(cardElement) {
        const id = cardElement.dataset.id;
        const name = cardElement.dataset.name;
        
        if (!id || !name) {
            console.warn('菜谱卡片缺少必要数据');
            return;
        }
        
        // 去除学校名称 (括号及其内容)
        const cleanName = name.replace(/（.*?）/, '').trim();
        
        const recipe = {
            id: id,
            name: cleanName,
            recipe_id: id,
            recipe_name: cleanName,
            is_custom: false
        };
        
        this.addToSelection(recipe);
    }

    // 添加自定义菜谱
    addCustomRecipe() {
        const input = document.getElementById('customDishInput');
        if (!input) return;
        
        const name = input.value.trim();
        if (!name) {
            console.warn('自定义菜品名称不能为空');
            return;
        }
        
        const recipe = {
            id: 'custom_' + Date.now(),
            name: name,
            recipe_id: null,
            recipe_name: name,
            is_custom: true
        };
        
        if (this.addToSelection(recipe)) {
            input.value = '';
        }
    }

    // 添加到已选菜品
    addToSelection(recipe) {
        if (this.selectedRecipes.has(recipe.id)) {
            this.highlightExisting(recipe.id);
            return false;
        }
        
        this.selectedRecipes.set(recipe.id, recipe);
        this.renderSelectedRecipe(recipe);
        return true;
    }

    // 渲染已选菜品
    renderSelectedRecipe(recipe) {
        const container = document.getElementById('selectedDishes');
        if (!container) return;
        
        const tag = document.createElement('div');
        tag.className = 'selected-recipe-tag';
        tag.dataset.id = recipe.id;
        tag.innerHTML = `
            ${recipe.name}
            <span class="remove-btn">&times;</span>
        `;
        
        container.appendChild(tag);
    }

    // 移除菜品
    removeRecipe(recipeId) {
        this.selectedRecipes.delete(recipeId);
        const tag = document.querySelector(`.selected-recipe-tag[data-id="${recipeId}"]`);
        if (tag) {
            tag.remove();
        }
    }

    // 高亮已存在的菜品
    highlightExisting(recipeId) {
        const tag = document.querySelector(`.selected-recipe-tag[data-id="${recipeId}"]`);
        if (tag) {
            tag.classList.add('highlight');
            setTimeout(() => tag.classList.remove('highlight'), 1000);
        }
    }

    // 清空已选显示
    clearSelectedDisplay() {
        const container = document.getElementById('selectedDishes');
        if (container) {
            container.innerHTML = '';
        }
    }

    // 加载当前菜品
    loadCurrentRecipes(date, meal) {
        const currentRecipes = MenuDataManager.getRecipes(date, meal);
        currentRecipes.forEach(recipe => {
            this.addToSelection(recipe);
        });
    }

    // 保存选择
    saveSelection() {
        const { currentDate, currentMeal } = this.state;
        
        if (!currentDate || !currentMeal) {
            console.error('缺少日期或餐次信息');
            return;
        }
        
        const recipes = Array.from(this.selectedRecipes.values());
        
        // 更新数据管理器
        MenuDataManager.setRecipes(currentDate, currentMeal, recipes);
        
        // 更新UI显示
        const input = document.querySelector(`.menu-input[data-date="${currentDate}"][data-meal="${currentMeal}"]`);
        if (input) {
            UIManager.updateInputDisplay(input, recipes);
            
            // 保存菜品ID到data属性
            const recipeIds = recipes.map(r => r.recipe_id || r.id || null);
            input.dataset.recipeIds = JSON.stringify(recipeIds);
        }
        
        // 关闭模态框
        if (this.modal) {
            this.modal.hide();
        }
        
        // 显示成功消息
        UIManager.showMessage('菜品已选择，请点击"保存菜单"按钮保存到数据库', 'info');
    }
}

// 创建全局实例
window.RecipeSelectorV3 = new RecipeSelectorV3();

// 确保新版选择器覆盖老版本
console.log('新版菜谱选择器 V3 已加载');

// 等待DOM完全加载后再覆盖老版本的事件绑定
$(document).ready(function() {
    console.log('重新绑定菜单输入框事件到新版选择器');

    // 移除老版本的事件绑定
    $('.menu-input').off('click');

    // 绑定到新版选择器
    $('.menu-input').on('click', function(e) {
        if ($(this).hasClass('readonly')) return;

        const date = $(this).data('date');
        const meal = $(this).data('meal');

        console.log(`使用新版选择器打开: ${date} ${meal}`);
        window.RecipeSelectorV3.showModal(date, meal);
    });
});
