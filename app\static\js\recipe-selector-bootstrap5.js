/**
 * 菜谱选择器 - Bootstrap 5.3.6 + 原生JavaScript 重写版本
 * 采用左侧固定导航栏+右侧流式内容区的布局模式
 * 使用Flexbox和Grid系统实现响应式布局
 */

class RecipeSelector {
    constructor() {
        this.state = {
            currentDate: null,
            currentMeal: null,
            selectedRecipes: new Map(),
            availableRecipes: [],
            currentSource: 'school',
            currentCategory: 'all',
            searchKeyword: ''
        };
        
        this.modal = null;
        this.init();
    }

    // 初始化
    init() {
        console.log('初始化Bootstrap 5.3.6菜谱选择器');
        this.initModal();
        this.bindEvents();
    }

    // 初始化模态框
    initModal() {
        const modalElement = document.getElementById('menuModal');
        if (modalElement && typeof bootstrap !== 'undefined') {
            this.modal = new bootstrap.Modal(modalElement, {
                backdrop: 'static',
                keyboard: true
            });
        }
    }

    // 绑定事件 - 使用原生JavaScript
    bindEvents() {
        // 搜索功能
        const searchInput = document.getElementById('recipeSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.state.searchKeyword = e.target.value.trim();
                this.filterRecipes();
            });
        }

        // 自定义菜品添加
        const addBtn = document.getElementById('addCustomDishBtn');
        const customInput = document.getElementById('customDishInput');
        
        if (addBtn) {
            addBtn.addEventListener('click', () => this.addCustomRecipe());
        }
        
        if (customInput) {
            customInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.addCustomRecipe();
                }
            });
        }

        // 来源筛选按钮
        document.addEventListener('click', (e) => {
            if (e.target.matches('#schoolRecipesBtn, #systemRecipesBtn')) {
                this.handleSourceFilter(e.target);
            }
        });

        // 分类筛选
        document.addEventListener('click', (e) => {
            if (e.target.matches('#recipeCategories .nav-link')) {
                e.preventDefault();
                this.handleCategoryFilter(e.target);
            }
        });

        // 菜品卡片点击
        document.addEventListener('click', (e) => {
            if (e.target.closest('.recipe-card')) {
                this.selectRecipe(e.target.closest('.recipe-card'));
            }
        });

        // 移除已选菜品
        document.addEventListener('click', (e) => {
            if (e.target.matches('.remove-recipe-btn')) {
                const recipeId = e.target.dataset.recipeId;
                this.removeRecipe(recipeId);
            }
        });

        // 保存选择
        const saveBtn = document.getElementById('saveSelectionBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveSelection());
        }

        // 菜单输入框点击事件
        document.addEventListener('click', (e) => {
            if (e.target.matches('.menu-input') && !e.target.classList.contains('readonly')) {
                const date = e.target.dataset.date;
                const meal = e.target.dataset.meal;
                this.showModal(date, meal);
            }
        });
    }

    // 显示模态框
    showModal(date, meal) {
        console.log(`显示菜谱选择器: ${date} ${meal}`);
        
        this.state.currentDate = date;
        this.state.currentMeal = meal;
        
        // 清空状态
        this.state.selectedRecipes.clear();
        this.state.searchKeyword = '';
        this.state.currentCategory = 'all';
        
        // 更新标题
        const titleElement = document.getElementById('modalTitle');
        if (titleElement) {
            titleElement.textContent = `${date} ${meal} 菜品选择`;
        }
        
        // 清空界面
        this.clearInterface();
        
        // 加载当前已选菜品
        this.loadCurrentRecipes(date, meal);
        
        // 显示模态框
        if (this.modal) {
            this.modal.show();
        }
        
        // 聚焦搜索框
        setTimeout(() => {
            const searchInput = document.getElementById('recipeSearch');
            if (searchInput) {
                searchInput.focus();
                searchInput.value = '';
            }
        }, 100);
        
        // 默认加载学校食谱
        this.loadRecipesBySource('school');
    }

    // 清空界面
    clearInterface() {
        // 清空已选菜品
        const selectedContainer = document.getElementById('selectedDishes');
        if (selectedContainer) {
            selectedContainer.innerHTML = '';
        }
        
        // 清空自定义输入框
        const customInput = document.getElementById('customDishInput');
        if (customInput) {
            customInput.value = '';
        }
        
        // 清空搜索框
        const searchInput = document.getElementById('recipeSearch');
        if (searchInput) {
            searchInput.value = '';
        }
        
        // 重置来源按钮
        document.querySelectorAll('#schoolRecipesBtn, #systemRecipesBtn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.getElementById('schoolRecipesBtn')?.classList.add('active');
        
        // 重置分类标签
        document.querySelectorAll('#recipeCategories .nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector('#recipeCategories .nav-link[data-category="all"]')?.classList.add('active');
    }

    // 按来源加载菜谱
    async loadRecipesBySource(source) {
        this.state.currentSource = source;
        
        try {
            const response = await fetch(`/api/recipes/filter?source=${source}`);
            const data = await response.json();
            
            if (data.success) {
                this.state.availableRecipes = this.flattenRecipes(data.data);
                this.updateCategoryTabs(data.data);
                this.renderRecipes();
            }
        } catch (error) {
            console.error('加载菜谱失败:', error);
        }
    }

    // 扁平化菜谱数据
    flattenRecipes(recipesByCategory) {
        const recipes = [];
        Object.values(recipesByCategory).forEach(categoryRecipes => {
            if (Array.isArray(categoryRecipes)) {
                recipes.push(...categoryRecipes);
            }
        });
        return recipes;
    }

    // 更新分类标签
    updateCategoryTabs(recipesByCategory) {
        const container = document.getElementById('recipeCategories');
        if (!container) return;
        
        container.innerHTML = '<li class="nav-item"><a class="nav-link active" data-category="all" href="#">全部</a></li>';
        
        Object.keys(recipesByCategory).forEach(category => {
            const li = document.createElement('li');
            li.className = 'nav-item';
            li.innerHTML = `<a class="nav-link" data-category="${category}" href="#">${category}</a>`;
            container.appendChild(li);
        });
    }

    // 渲染菜品网格
    renderRecipes() {
        const container = document.getElementById('recipesGrid');
        if (!container) return;
        
        container.innerHTML = '';
        
        const filteredRecipes = this.getFilteredRecipes();
        
        filteredRecipes.forEach(recipe => {
            const cardElement = this.createRecipeCard(recipe);
            container.appendChild(cardElement);
        });
    }

    // 获取筛选后的菜品
    getFilteredRecipes() {
        let filtered = [...this.state.availableRecipes];
        
        // 按分类筛选
        if (this.state.currentCategory !== 'all') {
            filtered = filtered.filter(recipe => recipe.category === this.state.currentCategory);
        }
        
        // 按关键词筛选
        if (this.state.searchKeyword) {
            const keyword = this.state.searchKeyword.toLowerCase();
            filtered = filtered.filter(recipe => 
                recipe.display_name.toLowerCase().includes(keyword)
            );
        }
        
        return filtered;
    }

    // 创建菜品卡片
    createRecipeCard(recipe) {
        const col = document.createElement('div');
        col.className = 'col-lg-3 col-md-4 col-sm-6';
        
        col.innerHTML = `
            <div class="card recipe-card h-100" data-recipe-id="${recipe.id}" data-recipe-name="${recipe.display_name}">
                <div class="card-body d-flex flex-column">
                    <h6 class="card-title flex-grow-1">${recipe.display_name}</h6>
                    <small class="text-muted">${recipe.category}</small>
                </div>
            </div>
        `;
        
        return col;
    }

    // 处理来源筛选
    handleSourceFilter(button) {
        // 更新按钮状态
        document.querySelectorAll('#schoolRecipesBtn, #systemRecipesBtn').forEach(btn => {
            btn.classList.remove('active');
        });
        button.classList.add('active');
        
        // 加载对应来源的菜谱
        const source = button.id === 'schoolRecipesBtn' ? 'school' : 'system';
        this.loadRecipesBySource(source);
    }

    // 处理分类筛选
    handleCategoryFilter(link) {
        // 更新标签状态
        document.querySelectorAll('#recipeCategories .nav-link').forEach(l => {
            l.classList.remove('active');
        });
        link.classList.add('active');
        
        // 更新状态并重新渲染
        this.state.currentCategory = link.dataset.category;
        this.renderRecipes();
    }

    // 筛选菜品
    filterRecipes() {
        this.renderRecipes();
    }

    // 选择菜品
    selectRecipe(cardElement) {
        const recipeId = cardElement.dataset.recipeId;
        const recipeName = cardElement.dataset.recipeName;
        
        if (!recipeId || !recipeName) {
            console.warn('菜品卡片缺少必要数据');
            return;
        }
        
        // 去除学校名称
        const cleanName = recipeName.replace(/（.*?）/, '').trim();
        
        const recipe = {
            id: recipeId,
            name: cleanName,
            recipe_id: recipeId,
            recipe_name: cleanName,
            is_custom: false
        };
        
        this.addToSelection(recipe);
    }

    // 添加自定义菜品
    addCustomRecipe() {
        const input = document.getElementById('customDishInput');
        if (!input) return;
        
        const name = input.value.trim();
        if (!name) {
            console.warn('自定义菜品名称不能为空');
            return;
        }
        
        const recipe = {
            id: 'custom_' + Date.now(),
            name: name,
            recipe_id: null,
            recipe_name: name,
            is_custom: true
        };
        
        if (this.addToSelection(recipe)) {
            input.value = '';
        }
    }

    // 添加到已选菜品
    addToSelection(recipe) {
        if (this.state.selectedRecipes.has(recipe.id)) {
            this.highlightExisting(recipe.id);
            return false;
        }
        
        this.state.selectedRecipes.set(recipe.id, recipe);
        this.renderSelectedRecipe(recipe);
        return true;
    }

    // 渲染已选菜品
    renderSelectedRecipe(recipe) {
        const container = document.getElementById('selectedDishes');
        if (!container) return;
        
        const tag = document.createElement('div');
        tag.className = 'badge bg-primary me-2 mb-2 p-2 position-relative';
        tag.dataset.recipeId = recipe.id;
        tag.innerHTML = `
            ${recipe.name}
            <button type="button" class="btn-close btn-close-white ms-2 remove-recipe-btn" 
                    data-recipe-id="${recipe.id}" aria-label="移除"></button>
        `;
        
        container.appendChild(tag);
    }

    // 移除菜品
    removeRecipe(recipeId) {
        this.state.selectedRecipes.delete(recipeId);
        const tag = document.querySelector(`[data-recipe-id="${recipeId}"]`);
        if (tag) {
            tag.remove();
        }
    }

    // 高亮已存在的菜品
    highlightExisting(recipeId) {
        const tag = document.querySelector(`[data-recipe-id="${recipeId}"]`);
        if (tag) {
            tag.classList.add('bg-warning');
            setTimeout(() => tag.classList.remove('bg-warning'), 1000);
        }
    }

    // 加载当前菜品
    loadCurrentRecipes(date, meal) {
        if (typeof MenuDataManager !== 'undefined') {
            const currentRecipes = MenuDataManager.getRecipes(date, meal);
            currentRecipes.forEach(recipe => {
                this.addToSelection(recipe);
            });
        }
    }

    // 保存选择
    saveSelection() {
        const { currentDate, currentMeal } = this.state;
        
        if (!currentDate || !currentMeal) {
            console.error('缺少日期或餐次信息');
            return;
        }
        
        const recipes = Array.from(this.state.selectedRecipes.values());
        
        // 更新数据管理器
        if (typeof MenuDataManager !== 'undefined') {
            MenuDataManager.setRecipes(currentDate, currentMeal, recipes);
        }
        
        // 更新UI显示
        const input = document.querySelector(`.menu-input[data-date="${currentDate}"][data-meal="${currentMeal}"]`);
        if (input && typeof UIManager !== 'undefined') {
            UIManager.updateInputDisplay(input, recipes);
            
            // 保存菜品ID到data属性
            const recipeIds = recipes.map(r => r.recipe_id || r.id || null);
            input.dataset.recipeIds = JSON.stringify(recipeIds);
        }
        
        // 关闭模态框
        if (this.modal) {
            this.modal.hide();
        }
        
        // 显示成功消息
        if (typeof UIManager !== 'undefined') {
            UIManager.showMessage('菜品已选择，请点击"保存菜单"按钮保存到数据库', 'info');
        }
    }
}

// 创建全局实例
window.RecipeSelector = new RecipeSelector();

// 覆盖全局selectRecipe函数
window.selectRecipe = function(recipeId, recipeName) {
    if (window.RecipeSelector) {
        const recipe = {
            id: recipeId,
            name: recipeName.replace(/（.*?）/, '').trim(),
            recipe_id: recipeId,
            recipe_name: recipeName.replace(/（.*?）/, '').trim(),
            is_custom: false
        };
        window.RecipeSelector.addToSelection(recipe);
    }
};
