2025-06-16 08:38:31,906 INFO: 应用启动 - PID: 19944 [in D:\StudentsCMSSP\app\__init__.py:839]
2025-06-16 08:44:00,872 INFO: 收到创建周菜单请求: b'{"area_id":"42","week_start":"2025-06-16"}' [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:76]
2025-06-16 08:44:00,873 INFO: 创建周菜单参数: area_id=42, week_start=2025-06-16 [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:87]
2025-06-16 08:44:00,873 INFO: 检查用户权限: user_id=34, area_id=42 [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:98]
2025-06-16 08:44:00,873 INFO: 用户角色: ['学校管理员'] [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:99]
2025-06-16 08:44:00,876 INFO: 权限检查结果: is_admin=0, is_school_admin=1, can_access_area=0, has_edit_permission=1 [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:106]
2025-06-16 08:44:00,877 INFO: 开始创建周菜单: area_id=42, week_start=2025-06-16, created_by=34 [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:123]
2025-06-16 08:44:00,877 INFO: 开始创建周菜单: area_id=42, week_start=2025-06-16, created_by=34 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:184]
2025-06-16 08:44:00,878 INFO: 转换后的日期对象: 2025-06-16, 类型=<class 'datetime.date'> [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:190]
2025-06-16 08:44:00,878 INFO: 计算的周结束日期: 2025-06-22 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:202]
2025-06-16 08:44:00,878 INFO: 检查是否已存在该周的菜单: area_id=42, week_start=2025-06-16 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:205]
2025-06-16 08:44:00,879 INFO: 获取周菜单: area_id=42, week_start=2025-06-16, 类型=<class 'datetime.date'> [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:42]
2025-06-16 08:44:00,879 INFO: 使用优化后的查询: area_id=42, week_start=2025-06-16 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:65]
2025-06-16 08:44:00,879 INFO: 执行主SQL查询: area_id=42, week_start_str=2025-06-16 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:77]
2025-06-16 08:44:00,881 INFO: 主查询未找到菜单: area_id=42, week_start=2025-06-16 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:102]
2025-06-16 08:44:00,881 INFO: 使用日期字符串: week_start_str=2025-06-16, week_end_str=2025-06-22 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:214]
2025-06-16 08:44:00,882 INFO: 准备执行SQL创建菜单 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:217]
2025-06-16 08:44:00,882 INFO: SQL参数: {'area_id': '42', 'week_start_str': '2025-06-16', 'week_end_str': '2025-06-22', 'status': '计划中', 'created_by': 34} [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:232]
2025-06-16 08:44:00,882 INFO: 执行SQL: 
            INSERT INTO weekly_menus (area_id, week_start, week_end, status, created_by)
            OUTPUT inserted.id
            VALUES (:area_id, CONVERT(DATETIME2(0), :week_start_str), CONVERT(DATETIME2(0), :week_end_str), :status, :created_by)
             [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:236]
2025-06-16 08:44:00,885 INFO: SQL执行成功，获取到ID: 39 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:243]
2025-06-16 08:44:00,886 INFO: 检查数据库连接状态 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:248]
2025-06-16 08:44:00,886 INFO: 数据库连接正常 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:251]
2025-06-16 08:44:00,887 INFO: 事务提交成功 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:258]
2025-06-16 08:44:00,887 INFO: 菜单缓存已清理 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-16 08:44:00,890 INFO: 验证成功: 菜单已创建 ID=39 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:266]
2025-06-16 08:44:00,891 INFO: 周菜单创建成功: id=39 [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:125]
2025-06-16 08:44:00,891 INFO: 返回创建周菜单响应: {'success': True, 'message': '周菜单创建成功', 'weekly_menu_id': 39, 'status': '计划中'} [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:142]
2025-06-16 08:48:03,838 INFO: 当前用户: 18373062333 [in D:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-16 08:48:03,838 INFO: 用户区域ID: 42 [in D:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-16 08:48:03,840 INFO: 用户区域名称: 朝阳区实验中学 [in D:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-16 08:48:03,840 INFO: 是否管理员: 0 [in D:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-16 08:53:28,188 INFO: 当前用户: 18373062333 [in D:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-16 08:53:28,188 INFO: 用户区域ID: 42 [in D:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-16 08:53:28,189 INFO: 用户区域名称: 朝阳区实验中学 [in D:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-16 08:53:28,189 INFO: 是否管理员: 0 [in D:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-16 08:54:49,269 INFO: 查询菜谱：日期=2025-06-16, 星期=0(0=周一), day_of_week=1, 餐次=午餐, 区域ID=42 [in D:\StudentsCMSSP\app\routes\food_trace.py:324]
2025-06-16 08:54:49,272 INFO: 找到 0 个周菜单 [in D:\StudentsCMSSP\app\routes\food_trace.py:334]
2025-06-16 08:54:49,272 INFO: 未找到 2025-06-16 午餐 的菜谱信息 [in D:\StudentsCMSSP\app\routes\food_trace.py:365]
2025-06-16 08:54:49,272 INFO: 食材一致性分析完成: 匹配率=0%, 缺失=0, 多余=0 [in D:\StudentsCMSSP\app\routes\food_trace.py:531]
2025-06-16 09:02:17,262 INFO: 收到创建周菜单请求: b'{"area_id":"44","week_start":"2025-06-16"}' [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:76]
2025-06-16 09:02:17,263 INFO: 创建周菜单参数: area_id=44, week_start=2025-06-16 [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:87]
2025-06-16 09:02:17,263 INFO: 检查用户权限: user_id=38, area_id=44 [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:98]
2025-06-16 09:02:17,263 INFO: 用户角色: ['学校管理员'] [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:99]
2025-06-16 09:02:17,265 INFO: 权限检查结果: is_admin=0, is_school_admin=1, can_access_area=0, has_edit_permission=1 [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:106]
2025-06-16 09:02:17,265 INFO: 开始创建周菜单: area_id=44, week_start=2025-06-16, created_by=38 [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:123]
2025-06-16 09:02:17,266 INFO: 开始创建周菜单: area_id=44, week_start=2025-06-16, created_by=38 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:184]
2025-06-16 09:02:17,266 INFO: 转换后的日期对象: 2025-06-16, 类型=<class 'datetime.date'> [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:190]
2025-06-16 09:02:17,266 INFO: 计算的周结束日期: 2025-06-22 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:202]
2025-06-16 09:02:17,266 INFO: 检查是否已存在该周的菜单: area_id=44, week_start=2025-06-16 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:205]
2025-06-16 09:02:17,266 INFO: 获取周菜单: area_id=44, week_start=2025-06-16, 类型=<class 'datetime.date'> [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:42]
2025-06-16 09:02:17,266 INFO: 使用优化后的查询: area_id=44, week_start=2025-06-16 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:65]
2025-06-16 09:02:17,266 INFO: 执行主SQL查询: area_id=44, week_start_str=2025-06-16 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:77]
2025-06-16 09:02:17,267 INFO: 主查询未找到菜单: area_id=44, week_start=2025-06-16 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:102]
2025-06-16 09:02:17,267 INFO: 使用日期字符串: week_start_str=2025-06-16, week_end_str=2025-06-22 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:214]
2025-06-16 09:02:17,267 INFO: 准备执行SQL创建菜单 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:217]
2025-06-16 09:02:17,267 INFO: SQL参数: {'area_id': '44', 'week_start_str': '2025-06-16', 'week_end_str': '2025-06-22', 'status': '计划中', 'created_by': 38} [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:232]
2025-06-16 09:02:17,267 INFO: 执行SQL: 
            INSERT INTO weekly_menus (area_id, week_start, week_end, status, created_by)
            OUTPUT inserted.id
            VALUES (:area_id, CONVERT(DATETIME2(0), :week_start_str), CONVERT(DATETIME2(0), :week_end_str), :status, :created_by)
             [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:236]
2025-06-16 09:02:17,268 INFO: SQL执行成功，获取到ID: 40 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:243]
2025-06-16 09:02:17,268 INFO: 检查数据库连接状态 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:248]
2025-06-16 09:02:17,269 INFO: 数据库连接正常 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:251]
2025-06-16 09:02:17,270 INFO: 事务提交成功 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:258]
2025-06-16 09:02:17,270 INFO: 菜单缓存已清理 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-16 09:02:17,272 INFO: 验证成功: 菜单已创建 ID=40 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:266]
2025-06-16 09:02:17,273 INFO: 周菜单创建成功: id=40 [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:125]
2025-06-16 09:02:17,274 INFO: 返回创建周菜单响应: {'success': True, 'message': '周菜单创建成功', 'weekly_menu_id': 40, 'status': '计划中'} [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:142]
